<template>
  <view class="h-full from-#f8fafc to-#ffffff bg-gradient-to-b">
    <page-paging ref="pagingRef" refresher-only @on-refresh="onRefreshData">
      <!-- 顶部背景区域 -->
      <view class="header-section">
        <view class="header-bg" />

        <!-- 导航栏 -->
        <view class="header-content">
          <wd-navbar custom-class="custom-navbar-class" :bordered="false" safe-area-inset-top>
            <template #title>
              <view class="user-info" @click="handleToUserCenter">
                <view class="user-avatar">
                  <i class="i-mdi-account-circle-outline" />
                </view>
                <view class="user-details">
                  <text class="user-phone">
                    {{ maskPhoneNumber(loginUser.account as string) }}
                  </text>
                  <text class="user-greeting">
                    欢迎使用万商云Pro
                  </text>
                </view>
              </view>
            </template>
          </wd-navbar>
        </view>

        <!-- 功能菜单 -->
        <view class="menu-section">
          <view class="menu-grid">
            <view
              v-for="(item, key) in menus"
              :key="key"
              class="menu-item"
              @click="toMenuUrl(item)"
            >
              <view class="menu-icon-wrapper">
                <i class="menu-icon" :class="item.icon_class" />
              </view>
              <text class="menu-text">
                {{ item.menu_name }}
              </text>
            </view>
          </view>
        </view>
      </view>

      <!-- 额度使用情况卡片 -->
      <view class="quota-card">
        <view class="card-header">
          <view class="card-title">
            <i class="i-mdi-chart-line title-icon" />
            <text class="title-text">
              本月额度使用情况
            </text>
          </view>
          <view class="detail-link" @click="toTransRule">
            <text class="detail-text">
              详情
            </text>
            <i class="i-mdi-chevron-right detail-arrow" />
          </view>
        </view>

        <!-- 刷卡交易 -->
        <view class="transaction-section">
          <view class="section-header">
            <view class="section-icon pos-icon" />
            <text class="section-title">
              刷卡交易
            </text>
          </view>

          <view class="progress-item">
            <view class="progress-info">
              <view class="info-item">
                <text class="info-label">
                  当日已用
                </text>
                <text class="info-amount">
                  ¥{{ formatCurrency(cumulateLimitInfo.posDayCumulate) }}
                </text>
              </view>
              <view class="info-item">
                <text class="info-label">
                  日限额
                </text>
                <text class="info-limit">
                  {{ formatAmount(cumulateLimitInfo.posDayPayLimit) }}
                </text>
              </view>
            </view>
            <view class="progress-wrapper">
              <wd-progress
                :percentage="calculatePercentage(cumulateLimitInfo.posDayCumulate, cumulateLimitInfo.posDayPayLimit)"
                hide-text
                custom-class="custom-progress"
              />
            </view>
          </view>

          <view class="progress-item">
            <view class="progress-info">
              <view class="info-item">
                <text class="info-label">
                  当月汇总
                </text>
                <text class="info-amount">
                  ¥{{ formatCurrency(cumulateLimitInfo.posMonthCumulate) }}
                </text>
              </view>
              <view class="info-item">
                <text class="info-label">
                  月限额
                </text>
                <text class="info-limit">
                  {{ formatAmount(cumulateLimitInfo.posMonthPayLimit) }}
                </text>
              </view>
            </view>
            <view class="progress-wrapper">
              <wd-progress
                :percentage="calculatePercentage(cumulateLimitInfo.posMonthCumulate, cumulateLimitInfo.posMonthPayLimit)"
                hide-text
                custom-class="custom-progress"
              />
            </view>
          </view>
        </view>

        <!-- 扫码交易 -->
        <view class="transaction-section">
          <view class="section-header">
            <view class="section-icon qr-icon" />
            <text class="section-title">
              扫码交易
            </text>
          </view>

          <view class="progress-item">
            <view class="progress-info">
              <view class="info-item">
                <text class="info-label">
                  当日已用
                </text>
                <text class="info-amount">
                  ¥{{ formatCurrency(cumulateLimitInfo.qrcDayCumulate) }}
                </text>
              </view>
              <view class="info-item">
                <text class="info-label">
                  日限额
                </text>
                <text class="info-limit">
                  {{ formatAmount(cumulateLimitInfo.qrcDayPayLimit) }}
                </text>
              </view>
            </view>
            <view class="progress-wrapper">
              <wd-progress
                :percentage="calculatePercentage(cumulateLimitInfo.qrcDayCumulate, cumulateLimitInfo.qrcDayPayLimit)"
                hide-text
                custom-class="custom-progress"
              />
            </view>
          </view>

          <view class="progress-item">
            <view class="progress-info">
              <view class="info-item">
                <text class="info-label">
                  当月已用
                </text>
                <text class="info-amount">
                  ¥{{ formatCurrency(cumulateLimitInfo.qrcMonthCumulate) }}
                </text>
              </view>
              <view class="info-item">
                <text class="info-label">
                  月限额
                </text>
                <text class="info-limit">
                  {{ formatAmount(cumulateLimitInfo.qrcMonthPayLimit) }}
                </text>
              </view>
            </view>
            <view class="progress-wrapper">
              <wd-progress
                :percentage="calculatePercentage(cumulateLimitInfo.qrcMonthCumulate, cumulateLimitInfo.qrcMonthPayLimit)"
                hide-text
                custom-class="custom-progress"
              />
            </view>
          </view>
        </view>
      </view>
    </page-paging>
    <wd-message-box />
  </view>
</template>

<script setup lang="ts">
import NP from 'number-precision';
import { useMessage } from 'wot-design-uni';
import { isNumber } from 'wot-design-uni/components/common/util';
import { MerchApi } from '@/api/merch/index';
import { useUserStore } from '@/store';

defineOptions({
  options: {
    styleIsolation: 'shared', // 启用共享样式
  },
});

const message = useMessage();

// 登录用户信息
const loginUser = computed(() => useUserStore().info);

const pagingRef = ref();

// 累计额度信息
const cumulateLimitInfo = ref({
  posDayCumulate: 0,
  posDayPayLimit: 0,
  posMonthCumulate: 0,
  posMonthPayLimit: 0,
  qrcDayCumulate: 0,
  qrcDayPayLimit: 0,
  qrcMonthCumulate: 0,
  qrcMonthPayLimit: 0,
});

// 菜单列表
interface IMenu {
  menu_name: string;
  icon_class: string;
  to: string; // 菜单跳转地址
};
const menus: IMenu[] = [
  {
    menu_name: '商户报备',
    icon_class: 'i-mdi-account-arrow-up-outline',
    to: '/pages/report/merch-report/index',
  },
  {
    menu_name: '交易管理',
    icon_class: 'i-mdi-text-box-outline',
    to: '/pages/trans/index',
  },
  {
    menu_name: '设备管理',
    icon_class: 'i-mdi-calculator',
    to: '/pages/terminal/index',
  },
  {
    menu_name: '结算卡管理',
    icon_class: 'i-mdi-credit-card',
    to: '/pages/settle-info/index',
  },
];

const merchStatus = ref<null | number>(null);

onLoad(() => {
  onRefreshData();
});

function onRefreshData() {
  Promise.allSettled([
    queryMerchStatus(),
    queryCumulateLimitInfo(),
  ]).then(() => {
    pagingRef.value?.complete();
  });
}

/**
 * 查询累计额度信息
 */
async function queryCumulateLimitInfo() {
  const data = await MerchApi.cumulateLimitDetail();
  cumulateLimitInfo.value = Object.assign({}, data, data?.cumulate);
}

// 手机号脱敏
function maskPhoneNumber(phoneNumber: string): string {
  // 检查手机号是否有效
  if (!/^\d{11}$/.test(phoneNumber)) {
    return phoneNumber;
  }

  // 对手机号进行脱敏处理
  const masked = phoneNumber.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2');

  return masked;
}

/**
 * 计算百分比
 * @param value
 * @param totalValue
 */
function calculatePercentage(value: number, totalValue: number) {
  if (!value || !totalValue) {
    return 0;
  }
  // 关闭边界检查
  NP.enableBoundaryChecking(false);
  // 计算百分比
  return NP.round(
    NP.divide(value, totalValue),
    2,
  ) * 100;
}

/**
 * 金额转换
 */
function formatAmount(amount: number) {
  if (!amount)
    return 0;

  amount = Number(amount);
  if (amount < 10000) {
    return amount;
  }
  // 保留两位小数
  else {
    return `${NP.round(NP.divide(amount, 10000), 2)}万`;
  }
}

/**
 * 格式化货币显示
 */
function formatCurrency(amount: number) {
  if (!amount)
    return '0.00';

  const num = Number(amount);
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
}

async function queryMerchStatus() {
  const data = await MerchApi.queryStatus();
  merchStatus.value = data?.authStatus;
}

function showNeedVertOrderPopup() {
  message
    .confirm({
      msg: '您存在待支付权益订单, 支付前需要进行意愿确认, 点击确定跳转至意愿确认权益订单列表; 取消后您也可以在“我的-意愿核身订单”查看',
      title: '温馨提示',
    })
    .then(() => {
      uni.navigateTo({ url: '/pages/user/wish-confirm-order' });
    });
}

function toMenuUrl({ to: url }: IMenu) {
  console.log(url);
  if (!isNumber(merchStatus.value)) {
    message.alert({
      msg: '商户状态未知, 请稍后再试',
      title: '提示',
    });
    return;
  }

  switch (merchStatus.value) {
    case 0:
      message.alert({
        msg: '尚未完成实名认证, 点击按钮开始认证',
        title: '温馨提示',
      }).then(() => {
        uni.navigateTo({ url: '/pages/report/merch-auth/auth-micro-merch/index' });
      });
      break;
    case 1:
    case 3:
      message.alert({
        msg: '认证信息审核中, 请耐心等待',
        title: '温馨提示',
      });
      break;
    case 2:
      message.alert({
        msg: '认证信息审核未通过',
        title: '温馨提示',
        confirmButtonText: '查看详情',
      }).then(() => {
        uni.navigateTo({ url: '/pages/report/merch-auth/auth-result' });
      });
      break;
    default:
      uni.navigateTo({ url });
  }
}

function handleToUserCenter() {
  uni.switchTab({ url: '/pages/tab/user/index' });
}

function toTransRule() {
  uni.navigateTo({ url: '/pages/trans/trans-rule' });
}
</script>

<style lang="scss" scoped>
// 顶部区域样式
.header-section {
  position: relative;
  overflow: hidden;
  padding-bottom: 100rpx;
  // 为固定高度的导航栏预留足够空间
  min-height: 400rpx;
}

.header-bg {
  position: absolute;
  top: 0;
  left: -20%;
  z-index: 0;
  width: 140%;
  height: 100%;
  background: linear-gradient(135deg, #517cf0 0%, #254bb2 100%);
  border-bottom-left-radius: 50%;
  border-bottom-right-radius: 50%;
}

.header-content {
  position: relative;
  z-index: 1;
  // 导航栏区域，高度由wd-navbar组件自动处理
  padding-bottom: 30rpx;
}

// 用户信息样式
.user-info {
  display: flex;
  align-items: center;
  padding: 0 30rpx;
  color: white;
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:active {
    opacity: 0.8;
  }
}

.user-avatar {
  margin-right: 20rpx;

  i {
    font-size: 80rpx;
    opacity: 0.9;
  }
}

.user-details {
  display: flex;
  flex-direction: column;

  .user-phone {
    margin-bottom: 4rpx;
    font-size: 36rpx;
    font-weight: 600;
  }

  .user-greeting {
    font-size: 26rpx;
    opacity: 0.8;
  }
}

// 菜单样式
.menu-section {
  position: relative;
  z-index: 1;
  // 适配固定导航栏高度，确保菜单位置合适
  padding: 20rpx 20rpx 0;
  margin-top: 20rpx;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  justify-items: center;
}

.menu-item {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24rpx 12rpx 20rpx;
  width: 100%;
  max-width: 140rpx;
  transition: all 0.3s ease;
  flex-direction: column;
  cursor: pointer;

  &:active {
    transform: scale(0.95);
  }
}

.menu-icon-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20rpx;
  width: 96rpx;
  height: 96rpx;
  background: rgb(255 255 255 / 18%);
  border: 1px solid rgb(255 255 255 / 25%);
  border-radius: 24rpx;
  backdrop-filter: blur(12rpx);
  box-shadow: 0 4rpx 12rpx rgb(0 0 0 / 10%);
}

.menu-icon {
  font-size: 48rpx;
  color: white;
  opacity: 0.95;
}

.menu-text {
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  width: 100%;
  min-height: 64rpx;
  font-size: 26rpx;
  text-align: center;
  color: white;
  font-weight: 500;
  line-height: 1.2;
  word-break: keep-all;
}

// 额度卡片样式
.quota-card {
  position: relative;
  z-index: 2;
  overflow: hidden;
  margin: -80rpx 24rpx 24rpx;
  background: white;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgb(0 0 0 / 8%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 32rpx 24rpx;
  border-bottom: 1px solid #f5f5f5;
}

.card-title {
  display: flex;
  align-items: center;

  .title-icon {
    margin-right: 12rpx;
    font-size: 36rpx;
    color: #517cf0;
  }

  .title-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #1a1a1a;
  }
}

.detail-link {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.2s ease;

  &:active {
    opacity: 0.7;
  }

  .detail-text {
    margin-right: 4rpx;
    font-size: 28rpx;
    color: #517cf0;
  }

  .detail-arrow {
    font-size: 32rpx;
    color: #517cf0;
  }
}

// 交易区块样式
.transaction-section {
  padding: 24rpx 32rpx;

  &:not(:last-child) {
    border-bottom: 1px solid #f5f5f5;
  }
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-icon {
  margin-right: 16rpx;
  width: 8rpx;
  height: 32rpx;
  border-radius: 4rpx;

  &.pos-icon {
    background: linear-gradient(135deg, #517cf0, #254bb2);
  }

  &.qr-icon {
    background: linear-gradient(135deg, #4d80f0, #2bb3ed);
  }
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}

// 进度条项目样式
.progress-item {
  margin-bottom: 32rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
}

.info-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;

  &:last-child {
    align-items: flex-end;
  }
}

.info-label {
  margin-bottom: 8rpx;
  font-size: 24rpx;
  color: #999;
}

.info-amount {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.info-limit {
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
}

.progress-wrapper {
  margin-top: 8rpx;
}

// 导航栏自定义样式
:deep(.custom-navbar-class) {
  background-color: transparent !important;
  // 确保导航栏高度由组件自动处理，不强制设置高度

  .wd-navbar__left {
    padding: 0 !important;
  }

  .wd-navbar__title {
    // 确保标题内容垂直居中
    display: flex;
    align-items: center;
    margin: 0;
    max-width: 100%;
    color: white !important;
  }

  // 适配不同平台的状态栏高度
  .wd-navbar__content {
    min-height: auto;
  }
}

// 进度条自定义样式
:deep(.custom-progress) {
  .wd-progress__outer {
    height: 8rpx;
    background-color: #f0f0f0;
    border-radius: 4rpx;
  }

  .wd-progress__inner {
    background: linear-gradient(90deg, #517cf0 0%, #254bb2 100%);
    border-radius: 4rpx;
  }
}
</style>
